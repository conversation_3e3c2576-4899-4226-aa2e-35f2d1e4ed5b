package com.hukapp.service.auth.modules.app.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

import java.time.LocalDate;
import java.util.List;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
@Schema(description = "Request object for creating a power of attorney")
public class PowerOfAttorneyCreateDto {
    
    @Schema(description = "Power of attorney number", example = "POA-2023-12345")
    @NotBlank(message = "Vekaletname numarası boş olamaz")
    private String powerOfAttorneyNumber;
    
    @Schema(description = "Notary name", example = "Istanbul 5. Noterliği")
    @NotBlank(message = "Noter adı boş olamaz")
    private String notaryName;
    
    @Schema(description = "List of client IDs", example = "[1, 2, 3]")
    private List<Long> clientIds;
    
    @Schema(description = "List of lawyer names", example = "[\"Av. Ayşe Demir\", \"Av. Ali Yıldız\"]")
    @NotEmpty(message = "Avukat listesi boş olamaz")
    private List<String> lawyerList;
    
    @Schema(description = "List of powers granted", example = "[\"Dava açma\", \"Sulh olma\", \"Feragat\"]")
    @NotEmpty(message = "Yetki listesi boş olamaz")
    private List<String> powerList;
    
    @Schema(description = "Yevmiye number", example = "12345")
    @NotBlank(message = "Yevmiye numarası boş olamaz")
    private String yevmiyeNo;
    
    @Schema(description = "Start date of the power of attorney", example = "2023-01-15")
    @NotNull(message = "Başlangıç tarihi boş olamaz")
    private LocalDate startDate;
    
    @Schema(description = "End date of the power of attorney", example = "2024-01-15")
    private LocalDate endDate;
    
    @Schema(description = "List of case numbers associated with this power of attorney", example = "[\"2023/123\", \"2023/456\"]")
    private List<String> caseNumbers;

    @NotNull(message = "Müvekkil adları boş olamaz")
    private List<String> nameOfClientsInPowerOfAttorney;
}
