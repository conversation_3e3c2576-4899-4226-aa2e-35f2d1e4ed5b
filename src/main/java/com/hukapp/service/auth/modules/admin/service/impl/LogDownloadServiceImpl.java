package com.hukapp.service.auth.modules.admin.service.impl;

import com.hukapp.service.auth.common.exception.custom.ResourceNotFoundException;
import com.hukapp.service.auth.modules.admin.dto.response.LogFileDownloadDto;
import com.hukapp.service.auth.modules.admin.exception.LogFileAccessException;
import com.hukapp.service.auth.modules.admin.service.LogDownloadService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.ZoneId;

/**
 * Implementation of LogDownloadService for secure log file access
 */
@Service
@Slf4j
public class LogDownloadServiceImpl implements LogDownloadService {

    private static final String LOG_FILE_PATH = "logs/server.log";
    private static final String CONTENT_TYPE = "text/plain";
    private static final String LOG_NOT_FOUND_MESSAGE = "Server log file not found or not accessible: ";

    @Override
    public LogFileDownloadDto downloadServerLog(String adminEmail) {
        log.info("Admin log download request from user: {}", adminEmail);
        
        Path logPath = Paths.get(LOG_FILE_PATH);
        
        if (!Files.exists(logPath)) {
            log.warn("Server log file not found at path: {} for admin: {}", LOG_FILE_PATH, adminEmail);
            throw new ResourceNotFoundException(LOG_NOT_FOUND_MESSAGE + LOG_FILE_PATH);
        }
        
        if (!Files.isReadable(logPath)) {
            log.error("Server log file is not readable at path: {} for admin: {}", LOG_FILE_PATH, adminEmail);
            throw new ResourceNotFoundException("Server log file is not accessible");
        }
        
        try {
            byte[] content = Files.readAllBytes(logPath);
            long fileSize = Files.size(logPath);
            LocalDateTime lastModified = LocalDateTime.ofInstant(
                Files.getLastModifiedTime(logPath).toInstant(), 
                ZoneId.systemDefault()
            );
            
            log.info("Server log file downloaded successfully by admin: {}. File size: {} bytes", 
                adminEmail, fileSize);
            
            LogFileDownloadDto downloadDto = new LogFileDownloadDto();
            downloadDto.setFilename("server.log");
            downloadDto.setContentType(CONTENT_TYPE);
            downloadDto.setFileSize(fileSize);
            downloadDto.setContent(content);
            downloadDto.setLastModified(lastModified);
            downloadDto.setFilePath(LOG_FILE_PATH);
            downloadDto.setAvailable(true);
            
            return downloadDto;
            
        } catch (IOException e) {
            log.error("Error reading server log file for admin: {}. Error: {}", adminEmail, e.getMessage(), e);
            throw new LogFileAccessException("Failed to read server log file: " + e.getMessage(), e);
        }
    }

    @Override
    public boolean isServerLogAvailable() {
        Path logPath = Paths.get(LOG_FILE_PATH);
        boolean exists = Files.exists(logPath);
        boolean readable = exists && Files.isReadable(logPath);
        
        log.debug("Server log availability check - exists: {}, readable: {}", exists, readable);
        return readable;
    }

    @Override
    public LogFileDownloadDto getServerLogInfo() {
        log.debug("Getting server log file information");
        
        Path logPath = Paths.get(LOG_FILE_PATH);
        LogFileDownloadDto info = new LogFileDownloadDto();
        info.setFilename("server.log");
        info.setContentType(CONTENT_TYPE);
        info.setFilePath(LOG_FILE_PATH);
        
        if (Files.exists(logPath) && Files.isReadable(logPath)) {
            try {
                long fileSize = Files.size(logPath);
                LocalDateTime lastModified = LocalDateTime.ofInstant(
                    Files.getLastModifiedTime(logPath).toInstant(), 
                    ZoneId.systemDefault()
                );
                
                info.setFileSize(fileSize);
                info.setLastModified(lastModified);
                info.setAvailable(true);
                
                log.debug("Server log info retrieved - size: {} bytes, last modified: {}", 
                    fileSize, lastModified);
                
            } catch (IOException e) {
                log.warn("Error getting server log file info: {}", e.getMessage());
                info.setAvailable(false);
            }
        } else {
            log.debug("Server log file not available");
            info.setAvailable(false);
        }
        
        return info;
    }
}
