package com.hukapp.service.auth.modules.admin.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * DTO for log file download containing file content and metadata
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Log file download response with content and metadata")
public class LogFileDownloadDto {

    @Schema(description = "Log filename", example = "server.log")
    private String filename;

    @Schema(description = "File content type/MIME type", example = "text/plain")
    private String contentType;

    @Schema(description = "File size in bytes", example = "1048576")
    private Long fileSize;

    @Schema(description = "File content as byte array")
    private byte[] content;

    @Schema(description = "Last modified timestamp")
    private LocalDateTime lastModified;

    @Schema(description = "File path for reference", example = "logs/server.log")
    private String filePath;

    @Schema(description = "Whether the file is available for download")
    private boolean available;
}
