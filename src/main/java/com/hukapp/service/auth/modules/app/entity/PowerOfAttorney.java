package com.hukapp.service.auth.modules.app.entity;

import com.hukapp.service.auth.common.converter.StringEncryptionConverter;
import com.hukapp.service.auth.common.entity.BaseEntity;
import com.hukapp.service.auth.modules.office.entity.Client;
import com.hukapp.service.auth.modules.person.entity.Person;

import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.ElementCollection;
import jakarta.persistence.Entity;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.JoinTable;
import jakarta.persistence.ManyToMany;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

import java.time.LocalDate;
import java.util.List;

@Entity
@Table(name = "power_of_attorneys")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class PowerOfAttorney extends BaseEntity {

    @Column(nullable = false, unique = true)
    private String powerOfAttorneyNumber;

    @Convert(converter = StringEncryptionConverter.class)
    @Column(nullable = false)
    private String notaryName;

    @ElementCollection
    private List<String> lawyerList;

    @ElementCollection
    private List<String> powerList;

    @ManyToMany
    @JoinTable(
        name = "power_of_attorney_clients",
        joinColumns = @JoinColumn(name = "power_of_attorney_id"),
        inverseJoinColumns = @JoinColumn(name = "client_id")
    )
    private List<Client> clientList;

    @Column(nullable = false)
    private String yevmiyeNo;

    @Column(nullable = false)
    private LocalDate startDate;

    private LocalDate endDate;

    @ElementCollection
    private List<String> caseNumbers;

    @ManyToOne
    @JoinColumn(name = "owner_id", nullable = false)
    private Person owner;

    @ElementCollection
    private List<String> nameOfClientsInPowerOfAttorney;
}
