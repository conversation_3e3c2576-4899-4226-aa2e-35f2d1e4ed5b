package com.hukapp.service.auth.modules.admin.controller;

import com.hukapp.service.auth.modules.admin.dto.response.LogFileDownloadDto;
import com.hukapp.service.auth.modules.admin.service.LogDownloadService;
import io.swagger.v3.oas.annotations.Operation;

import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Admin controller for downloading application log files
 * Provides secure access to server logs for administrators
 */
@RestController
@RequestMapping("/api/admin/logs")
@RequiredArgsConstructor
@Slf4j
@SecurityRequirement(name = "bearerAuth")
@Tag(name = "Admin Log Management", description = "Admin API for downloading and managing application log files")
@PreAuthorize("hasRole('ADMIN')")
public class AdminLogDownloadController {

    private final LogDownloadService logDownloadService;

    @GetMapping("/server/download")
    @Operation(
        summary = "Download server log file",
        description = "Download the current server.log file. This endpoint provides administrators with access to application logs for debugging and monitoring purposes. The log file contains application events, errors, and debug information."
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200", 
            description = "Log file downloaded successfully",
            content = @Content(
                mediaType = "text/plain",
                schema = @Schema(type = "string", format = "binary", description = "Log file content")
            )
        ),
        @ApiResponse(responseCode = "403", description = "Access denied - Admin role required"),
        @ApiResponse(responseCode = "404", description = "Log file not found or not accessible"),
        @ApiResponse(responseCode = "500", description = "Internal server error while reading log file")
    })
    public ResponseEntity<byte[]> downloadServerLog(Authentication authentication) {
        
        String adminEmail = authentication.getName();
        log.info("Admin log download request from user: {}", adminEmail);
        
        // Log this sensitive operation for audit purposes
        log.warn("ADMIN AUDIT: Server log file accessed by admin: {} at {}", 
            adminEmail, java.time.LocalDateTime.now());
        
        LogFileDownloadDto logDownload = logDownloadService.downloadServerLog(adminEmail);
        
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.TEXT_PLAIN);
        headers.setContentDispositionFormData("attachment", logDownload.getFilename());
        headers.setContentLength(logDownload.getFileSize());
        
        // Add security headers for file downloads
        headers.setCacheControl("no-cache, no-store, must-revalidate");
        headers.setPragma("no-cache");
        headers.setExpires(0);
        
        // Add content security headers
        headers.add("X-Content-Type-Options", "nosniff");
        headers.add("X-Frame-Options", "DENY");
        headers.add("X-XSS-Protection", "1; mode=block");
        
        // Add custom headers for log file metadata
        headers.add("X-Log-File-Size", logDownload.getFileSize().toString());
        headers.add("X-Log-Last-Modified", logDownload.getLastModified().toString());
        headers.add("X-Log-File-Path", logDownload.getFilePath());
        
        log.info("Server log file downloaded successfully by admin: {}. File size: {} bytes", 
            adminEmail, logDownload.getFileSize());
        
        return ResponseEntity.ok()
            .headers(headers)
            .body(logDownload.getContent());
    }

    @GetMapping("/server/info")
    @Operation(
        summary = "Get server log file information",
        description = "Get metadata about the server log file without downloading the content. Useful for checking file availability, size, and last modification time."
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200", 
            description = "Log file information retrieved successfully",
            content = @Content(schema = @Schema(implementation = LogFileDownloadDto.class))
        ),
        @ApiResponse(responseCode = "403", description = "Access denied - Admin role required"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<LogFileDownloadDto> getServerLogInfo(Authentication authentication) {
        
        String adminEmail = authentication.getName();
        log.debug("Admin log info request from user: {}", adminEmail);
        
        LogFileDownloadDto logInfo = logDownloadService.getServerLogInfo();
        
        log.debug("Server log info retrieved by admin: {}. Available: {}, Size: {} bytes", 
            adminEmail, logInfo.isAvailable(), logInfo.getFileSize());
        
        return ResponseEntity.ok(logInfo);
    }

    @GetMapping("/server/status")
    @Operation(
        summary = "Check server log availability",
        description = "Check if the server log file is available for download. Returns a simple boolean status."
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Log availability status retrieved"),
        @ApiResponse(responseCode = "403", description = "Access denied - Admin role required")
    })
    public ResponseEntity<Boolean> isServerLogAvailable(Authentication authentication) {
        
        String adminEmail = authentication.getName();
        log.debug("Admin log availability check from user: {}", adminEmail);
        
        boolean available = logDownloadService.isServerLogAvailable();
        
        log.debug("Server log availability checked by admin: {}. Available: {}", adminEmail, available);
        
        return ResponseEntity.ok(available);
    }
}
