package com.hukapp.service.auth.modules.admin.service;

import com.hukapp.service.auth.modules.admin.dto.response.LogFileDownloadDto;

/**
 * Service interface for log file download operations
 * Provides secure access to application log files for administrators
 */
public interface LogDownloadService {

    /**
     * Download the server log file
     * 
     * @param adminEmail the email of the admin requesting the download
     * @return the log file download response with content and metadata
     * @throws com.hukapp.service.auth.common.exception.ResourceNotFoundException if log file is not found
     * @throws java.io.IOException if there's an error reading the log file
     */
    LogFileDownloadDto downloadServerLog(String adminEmail);

    /**
     * Check if the server log file exists and is accessible
     * 
     * @return true if the log file exists and can be read, false otherwise
     */
    boolean isServerLogAvailable();

    /**
     * Get basic information about the server log file
     * 
     * @return log file metadata without content
     */
    LogFileDownloadDto getServerLogInfo();
}
